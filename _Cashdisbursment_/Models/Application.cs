using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Models
{
    public class Application
    {
        public int ApplicationID { get; set; }
        
        public int CompanyID { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Purpose { get; set; } = string.Empty;
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Requested cash must be greater than 0")]
        public double RequestedCash { get; set; }
        
        public bool IsDisbursed { get; set; } = false;
        
        [StringLength(255)]
        public string? DisbursedBy { get; set; }
        
        public DateTime DateRequested { get; set; } = DateTime.Now;
        
        public DateTime? DateDisbursed { get; set; }
        
        [StringLength(50)]
        public string Status { get; set; } = "Pending"; // Pending, Approved, Rejected
        
        public int ApprovalLevel { get; set; } = 0; // 0 = not started, 1+ = approval levels
        
        [StringLength(500)]
        public string? ApprovalComment { get; set; }
        
        [StringLength(500)]
        public string? PDFLocation { get; set; }
        
        // Navigation properties
        public Company? Company { get; set; }
        public List<Acquittal> Acquittals { get; set; } = new List<Acquittal>();
    }
}
