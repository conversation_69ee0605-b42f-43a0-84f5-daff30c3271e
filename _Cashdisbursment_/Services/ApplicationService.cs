using _Cashdisbursment_.Models;
using Microsoft.Data.SqlClient;

namespace _Cashdisbursment_.Services
{
    public class ApplicationService
    {
        private readonly DatabaseService _dbService;

        public ApplicationService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<List<Application>> GetApplicationsByCompanyAsync(int companyId)
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.CompanyID = @CompanyID
                ORDER BY a.DateRequested DESC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@CompanyID", companyId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

        public async Task<List<Application>> GetAllApplicationsAsync()
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                ORDER BY a.DateRequested DESC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

        public async Task<Application?> GetApplicationByIdAsync(int applicationId)
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.ApplicationID = @ApplicationID";

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@ApplicationID", applicationId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapApplicationFromReader(reader);
            }

            return null;
        }

        public async Task<int> CreateApplicationAsync(Application application)
        {
            const string query = @"
                INSERT INTO Applications (CompanyID, Description, Purpose, RequestedCash, 
                                        IsDisbursed, DateRequested, Status, ApprovalLevel, PDFLocation)
                OUTPUT INSERTED.ApplicationID
                VALUES (@CompanyID, @Description, @Purpose, @RequestedCash, 
                        @IsDisbursed, @DateRequested, @Status, @ApprovalLevel, @PDFLocation)";

            var parameters = new[]
            {
                new SqlParameter("@CompanyID", application.CompanyID),
                new SqlParameter("@Description", application.Description),
                new SqlParameter("@Purpose", application.Purpose),
                new SqlParameter("@RequestedCash", application.RequestedCash),
                new SqlParameter("@IsDisbursed", application.IsDisbursed),
                new SqlParameter("@DateRequested", application.DateRequested),
                new SqlParameter("@Status", application.Status),
                new SqlParameter("@ApprovalLevel", application.ApprovalLevel),
                new SqlParameter("@PDFLocation", (object?)application.PDFLocation ?? DBNull.Value)
            };

            var applicationId = await _dbService.ExecuteScalarAsync<int>(query, parameters);
            return applicationId;
        }

        public async Task<bool> UpdateApplicationApprovalAsync(int applicationId, int approvalLevel,
            string status, string? comment = null)
        {
            const string query = @"
                UPDATE Applications 
                SET ApprovalLevel = @ApprovalLevel, Status = @Status, ApprovalComment = @Comment
                WHERE ApplicationID = @ApplicationID";

            var parameters = new[]
            {
                new SqlParameter("@ApplicationID", applicationId),
                new SqlParameter("@ApprovalLevel", approvalLevel),
                new SqlParameter("@Status", status),
                new SqlParameter("@Comment", (object?)comment ?? DBNull.Value)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> DisburseApplicationAsync(int applicationId, string disbursedBy)
        {
            const string query = @"
                UPDATE Applications
                SET IsDisbursed = 1, DisbursedBy = @DisbursedBy, DateDisbursed = @DateDisbursed
                WHERE ApplicationID = @ApplicationID AND Status = 'Approved'";

            var parameters = new[]
            {
                new SqlParameter("@ApplicationID", applicationId),
                new SqlParameter("@DisbursedBy", disbursedBy),
                new SqlParameter("@DateDisbursed", DateTime.Now)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }



        public async Task<List<Application>> GetPendingApplicationsAsync()
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose,
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested,
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment,
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.Status = 'Pending'
                ORDER BY a.DateRequested ASC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

        public async Task<List<Application>> GetApprovedNotDisbursedApplicationsAsync()
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.Status = 'Approved' AND a.IsDisbursed = 0
                ORDER BY a.DateRequested ASC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

       private Application MapApplicationFromReader(SqlDataReader reader)
{
    return new Application
    {
        ApplicationID = reader.GetInt32(reader.GetOrdinal("ApplicationID")),
        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
        Description = reader.GetString(reader.GetOrdinal("Description")),
        Purpose = reader.GetString(reader.GetOrdinal("Purpose")),
        RequestedCash = reader.GetDouble(reader.GetOrdinal("RequestedCash")),
        IsDisbursed = reader.GetBoolean(reader.GetOrdinal("IsDisbursed")),
        DisbursedBy = reader.IsDBNull(reader.GetOrdinal("DisbursedBy")) ? null : reader.GetString(reader.GetOrdinal("DisbursedBy")),
        DateRequested = reader.GetDateTime(reader.GetOrdinal("DateRequested")),
        DateDisbursed = reader.IsDBNull(reader.GetOrdinal("DateDisbursed")) ? null : reader.GetDateTime(reader.GetOrdinal("DateDisbursed")),
        Status = reader.GetString(reader.GetOrdinal("Status")),
        ApprovalLevel = reader.GetInt32(reader.GetOrdinal("ApprovalLevel")),
        ApprovalComment = reader.IsDBNull(reader.GetOrdinal("ApprovalComment")) ? null : reader.GetString(reader.GetOrdinal("ApprovalComment")),
        PDFLocation = reader.IsDBNull(reader.GetOrdinal("PDFLocation")) ? null : reader.GetString(reader.GetOrdinal("PDFLocation")),
        Company = new Company
        {
            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
            Name = reader.GetString(reader.GetOrdinal("CompanyName"))
        }
    };
}

    }
}
