@page
@model _Cashdisbursment_.Pages.Admin.ApplicationsModel
@{
    ViewData["Title"] = "Manage Applications";
}

<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-2">Manage Applications</h1>
                <p class="text-muted mb-0">Review and process fund applications</p>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <input type="text" name="search" value="@Model.SearchTerm" class="form-control" 
                                       placeholder="Search by company or description...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="pending" selected="@(Model.StatusFilter == "pending")">Pending</option>
                                    <option value="approved" selected="@(Model.StatusFilter == "approved")">Approved</option>
                                    <option value="disbursed" selected="@(Model.StatusFilter == "disbursed")">Disbursed</option>
                                    <option value="rejected" selected="@(Model.StatusFilter == "rejected")">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Company</label>
                                <select name="companyId" class="form-select">
                                    <option value="">All Companies</option>
                                    @foreach (var company in Model.Companies)
                                    {
                                        <option value="@company.CompanyID" selected="@(Model.CompanyFilter == company.CompanyID)">
                                            @company.Name
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Date Range</label>
                                <select name="dateRange" class="form-select">
                                    <option value="">All Time</option>
                                    <option value="7" selected="@(Model.DateRange == "7")">Last 7 days</option>
                                    <option value="30" selected="@(Model.DateRange == "30")">Last 30 days</option>
                                    <option value="90" selected="@(Model.DateRange == "90")">Last 90 days</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row g-4 mb-section">
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-2x mb-3 text-primary"></i>
                        <h3>@Model.TotalApplications</h3>
                        <p>Total Applications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-3 text-warning"></i>
                        <h3>@Model.PendingApplications</h3>
                        <p>Pending Review</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                        <h3>@Model.ApprovedApplications</h3>
                        <p>Approved</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-3 text-info"></i>
                        <h3>$@Model.TotalAmount.ToString("N0")</h3>
                        <p>Total Requested</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Applications Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            Applications 
                            <span class="badge bg-secondary">@Model.Applications.Count</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Applications.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Company</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Level</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var app in Model.Applications)
                                        {
                                            <tr>
                                                <td><strong>#@app.ApplicationID</strong></td>
                                                <td>
                                                    <div class="fw-bold">@app.Company?.Name</div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                        @app.Description
                                                    </div>
                                                </td>
                                                <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                <td>
                                                    @if (app.IsDisbursed)
                                                    {
                                                        <span class="status-indicator status-disbursed">Disbursed</span>
                                                    }
                                                    else if (app.Status == "Approved")
                                                    {
                                                        <span class="status-indicator status-approved">Approved</span>
                                                    }
                                                    else if (app.Status == "Rejected")
                                                    {
                                                        <span class="status-indicator status-rejected">Rejected</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="status-indicator status-pending">Pending</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (app.ApprovalLevel > 0)
                                                    {
                                                        <span class="badge bg-info">Level @app.ApprovalLevel</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">Not Started</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a asp-page="/Admin/Applications/Details" asp-route-id="@app.ApplicationID" 
                                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        @if (app.Status == "Pending" && !app.IsDisbursed)
                                                        {
                                                            <a asp-page="/Admin/Applications/Review" asp-route-id="@app.ApplicationID"
                                                               class="btn btn-sm btn-warning" title="Review">
                                                                <i class="fas fa-gavel"></i>
                                                            </a>
                                                        }
                                                        @if (app.Status == "Approved" && !app.IsDisbursed)
                                                        {
                                                            <a asp-page="/Admin/Applications/Disburse" asp-route-id="@app.ApplicationID" 
                                                               class="btn btn-sm btn-success" title="Disburse">
                                                                <i class="fas fa-money-bill-wave"></i>
                                                            </a>
                                                        }
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                                <h6 class="text-muted">No applications found</h6>
                                <p class="text-muted">No applications match your search criteria.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
