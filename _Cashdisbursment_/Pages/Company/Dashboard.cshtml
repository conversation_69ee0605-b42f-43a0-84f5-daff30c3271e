@page
@model _Cashdisbursment_.Pages.Company.DashboardModel
@{
    ViewData["Title"] = "Company Dashboard";
}

<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-2">Dashboard</h1>
                <p class="text-muted mb-0">Welcome back, @Model.CurrentUser?.Company?.Name</p>
            </div>
        </div>
    </div>

    <div class="py-section">

        <!-- Statistics Cards -->
        <div class="row g-4 mb-section">
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-2x mb-3 text-primary"></i>
                        <h3>@Model.TotalApplications</h3>
                        <p>Total Applications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-3 text-warning"></i>
                        <h3>@Model.PendingApplications</h3>
                        <p>Pending Applications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                        <h3>@Model.ApprovedApplications</h3>
                        <p>Approved Applications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-3 text-info"></i>
                        <h3>$@Model.TotalDisbursed.ToString("N2")</h3>
                        <p>Total Disbursed</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-section">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <a asp-page="/Company/Applications/Create" class="btn btn-primary w-100">
                                    <i class="fas fa-plus me-2"></i>New Application
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a asp-page="/Company/Applications" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-list me-2"></i>View Applications
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a asp-page="/Company/Profile" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-user me-2"></i>Update Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Applications -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Applications</h5>
                        <a asp-page="/Company/Applications" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                <div class="card-body">
                    @if (Model.RecentApplications.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var app in Model.RecentApplications)
                                    {
                                        <tr>
                                            <td><strong>#@app.ApplicationID</strong></td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                    @app.Description
                                                </div>
                                            </td>
                                            <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                            <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                @if (app.IsDisbursed)
                                                {
                                                    <span class="status-disbursed">Disbursed</span>
                                                }
                                                else if (app.Status == "Approved")
                                                {
                                                    <span class="status-approved">Approved</span>
                                                }
                                                else if (app.Status == "Rejected")
                                                {
                                                    <span class="status-rejected">Rejected</span>
                                                }
                                                else
                                                {
                                                    <span class="status-pending">Pending</span>
                                                }
                                            </td>
                                            <td>
                                                <a asp-page="/Company/Applications/Details" asp-route-id="@app.ApplicationID"
                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                            <h6 class="text-muted">No applications found</h6>
                            <p class="text-muted mb-3">You haven't submitted any applications yet.</p>
                            <a asp-page="/Company/Applications/Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Your First Application
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
