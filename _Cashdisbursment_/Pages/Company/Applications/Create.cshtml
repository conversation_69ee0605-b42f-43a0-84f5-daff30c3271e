@page
@model _Cashdisbursment_.Pages.Company.Applications.CreateModel
@{
    ViewData["Title"] = "New Application";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-page="/Company/Dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a asp-page="/Company/Applications">Applications</a></li>
                    <li class="breadcrumb-item active">New Application</li>
                </ol>
            </nav>
            
            <h1 class="h3 mb-4">
                <i class="fas fa-plus me-2"></i>New Fund Application
            </h1>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Application Details</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="RequestedCash" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>Requested Amount *
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="RequestedCash" class="form-control" placeholder="0.00" step="0.01" required />
                                </div>
                                <span asp-validation-for="RequestedCash" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-building me-1"></i>Company
                                </label>
                                <input type="text" class="form-control" value="@Model.CurrentUser?.Company?.Name" readonly />
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Description *
                            </label>
                            <textarea asp-for="Description" class="form-control" rows="4" 
                                      placeholder="Provide a detailed description of your fund request..." required></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                            <div class="form-text">Describe what the funds will be used for in detail.</div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Purpose" class="form-label">
                                <i class="fas fa-bullseye me-1"></i>Purpose *
                            </label>
                            <textarea asp-for="Purpose" class="form-control" rows="3" 
                                      placeholder="Explain the purpose and expected outcomes..." required></textarea>
                            <span asp-validation-for="Purpose" class="text-danger"></span>
                            <div class="form-text">Explain the business purpose and expected outcomes.</div>
                        </div>

                        <div class="mb-4">
                            <label asp-for="SupportingDocument" class="form-label">
                                <i class="fas fa-paperclip me-1"></i>Supporting Document
                            </label>
                            <div class="file-upload-area">
                                <input asp-for="SupportingDocument" class="form-control" accept=".pdf,.doc,.docx" />
                                <div class="mt-2 text-center">
                                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">Upload supporting documents (PDF, DOC, DOCX)</p>
                                    <small class="text-muted">Maximum file size: 10MB</small>
                                </div>
                            </div>
                            <span asp-validation-for="SupportingDocument" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-page="/Company/Applications" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Application Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i>Tips for Success</h6>
                        <ul class="mb-0 small">
                            <li>Provide detailed and accurate information</li>
                            <li>Include supporting documents when possible</li>
                            <li>Clearly explain the business purpose</li>
                            <li>Specify expected outcomes and benefits</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock me-1"></i>Processing Time</h6>
                        <p class="mb-0 small">Applications typically take 3-5 business days to process through the approval workflow.</p>
                    </div>

                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-shield-alt me-1"></i>Required Information</h6>
                        <ul class="mb-0 small">
                            <li>Requested amount</li>
                            <li>Detailed description</li>
                            <li>Business purpose</li>
                            <li>Supporting documents (recommended)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // File upload preview
        document.querySelector('input[type="file"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const uploadArea = e.target.closest('.file-upload-area');
                const preview = uploadArea.querySelector('.text-center');
                preview.innerHTML = `
                    <i class="fas fa-file-pdf fa-2x text-success mb-2"></i>
                    <p class="text-success mb-0">${file.name}</p>
                    <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                `;
            }
        });
    </script>
}
