using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Approver
{
    public class ApplicationsModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly ApprovalService _approvalService;

        public ApplicationsModel(ApplicationService applicationService, ApprovalService approvalService)
        {
            _applicationService = applicationService;
            _approvalService = approvalService;
        }

        public User? CurrentUser { get; set; }
        public List<Application> Applications { get; set; } = new();
        public string CurrentFilter { get; set; } = "all";
        public int MyApprovalLevel { get; set; }

        public async Task<IActionResult> OnGetAsync(string filter = "all")
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsApprover(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            CurrentFilter = filter;
            await LoadApplicationsAsync();
            return Page();
        }

        private async Task LoadApplicationsAsync()
        {
            // Get approver's level
            var approvers = await _approvalService.GetAllApproversAsync();
            var currentApprover = approvers.FirstOrDefault(a => a.Email == CurrentUser!.Email);
            MyApprovalLevel = currentApprover?.ApproverNum ?? 0;

            // Get all applications
            var allApplications = await _applicationService.GetAllApplicationsAsync();

            // Filter based on the current filter
            switch (CurrentFilter.ToLower())
            {
                case "pending":
                    Applications = allApplications
                        .Where(a => a.Status == "Pending" && a.ApprovalLevel == MyApprovalLevel - 1)
                        .OrderByDescending(a => a.DateRequested)
                        .ToList();
                    break;
                case "approved":
                    Applications = allApplications
                        .Where(a => a.ApprovalLevel >= MyApprovalLevel)
                        .OrderByDescending(a => a.DateRequested)
                        .ToList();
                    break;
                default:
                    Applications = allApplications
                        .OrderByDescending(a => a.DateRequested)
                        .ToList();
                    break;
            }
        }
    }
}
