using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Approver
{
    public class DashboardModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly ApprovalService _approvalService;
        private readonly CompanyService _companyService;

        public DashboardModel(ApplicationService applicationService, ApprovalService approvalService, CompanyService companyService)
        {
            _applicationService = applicationService;
            _approvalService = approvalService;
            _companyService = companyService;
        }

        public User? CurrentUser { get; set; }
        public List<Application> PendingApplications { get; set; } = new();
        public List<Application> RecentApplications { get; set; } = new();
        public int TotalPendingCount { get; set; }
        public int MyApprovalLevel { get; set; }
        public double TotalPendingAmount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsApprover(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            await LoadDashboardDataAsync();
            return Page();
        }

        private async Task LoadDashboardDataAsync()
        {
            // Get approver's level
            var approvers = await _approvalService.GetAllApproversAsync();
            var currentApprover = approvers.FirstOrDefault(a => a.Email == CurrentUser!.Email);
            MyApprovalLevel = currentApprover?.ApproverNum ?? 0;

            // Get all applications
            var allApplications = await _applicationService.GetAllApplicationsAsync();

            // Filter applications that need this approver's attention
            // Applications that are at the previous approval level and need to move to this level
            PendingApplications = allApplications
                .Where(a => a.Status == "Pending" && a.ApprovalLevel == MyApprovalLevel - 1)
                .OrderByDescending(a => a.DateRequested)
                .Take(10)
                .ToList();

            // Recent applications this approver has processed
            RecentApplications = allApplications
                .Where(a => a.ApprovalLevel >= MyApprovalLevel)
                .OrderByDescending(a => a.DateRequested)
                .Take(10)
                .ToList();

            TotalPendingCount = PendingApplications.Count;
            TotalPendingAmount = PendingApplications.Sum(a => a.RequestedCash);
        }
    }
}
